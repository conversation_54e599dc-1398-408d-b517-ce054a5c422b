/* ************************************************************************
*  Copyright (C) 1990, 1991 - see 'license.doc' for complete information. *
************************************************************************* */


                     ITEM VALUE DOCUMENTATION
                           "values.doc"


These values are used, as illustrated below, with the different
item types. The various item types are shown in dbsup.doc.



ITEM_LIGHT (1)
Value[0]: Not Used
Value[1]: Not Used
Value[2]: Number of hours the light can be used for. Zero hours means that
          the light has gone out. A negative number will create an eternal
          light source.
Value[3]: Not Used


ITEM_SCROLL (2)
Value[0]: Level of the spell on the scroll.
Value[1]: Which spell (see list somewhere around the end of file)
Value[2]: Which spell
Value[3]: Which spell
  The values(1-3) are three (or less) different spells, mixed 'on' the scroll.
  Unused spells should be set to -1.


ITEM_WAND (3)
Value[0]: Level of spell in wand.
Value[1]: Max Charges (1..X)
Value[2]: Charges Left
Value[3]: Which spell in wand (see list somewhere around the end of file)


ITEM_STAFF (4)
Value[0]: Level of spell in staff.
Value[1]: Max Charges (1..X)
Value[2]: Charges Left
Value[3]: Which spell in staff (see list somewhere around the end of file)


ITEM_WEAPON (5)
Value[0]: Not Used
Value[1]: Number of dice to roll for damage
Value[2]: Size of dice to roll for damage
Value[3]: The weapon type. Type is one of:

         NUMBER  CATEGORY   Message type
            2  : Slash         "whip/whips"
            3  : Slash         "slash/slashes"

            6  : Bludgeon      "crush/crushes"
            7  : Bludgeon      "pound/pounds"

           11  : Pierce        "pierce/pierces"

          New types can be added as needed.


ITEM_FIREWEAPON (6)
Value[0]: -
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_MISSILE    (7)
Value[0]: -
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_TREASURE   (8)
Value[0]: -
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_ARMOR      (9)
Value[0]: The effective AC. >0 enhances the armour class. <0 reduces the
          the armour class (cursed armour for example).
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_POTION    (10)
Value[0]: Level of the spell in the potion.
Value[1]: Which spell (Listed elsewhere in this file)
Value[2]: Which spell
Value[3]: Which spell
  The values(1-3) are three (or less) different spells, mixed in the potion.
  Unused spells should be set to -1.
   Eg.
   Value 0 : 30  (Level)
   Value 1 : 27  (Harm)
   Value 2 : 17  (Curse)
   Value 3 :  4  (Blindness)
     (* Don't drink this - It's bad for your health! *)


ITEM_WORN      (11)
Value[0]: -
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_OTHER     (12)
Value[0]: -
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_TRASH     (13)
Value[0]: -
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_TRAP      (14)
Value[0]: -
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_CONTAINER (15)
Value[0]: Maximum weight the container can contain.
Value[1]: Container flags:

   CLOSEABLE     1
   PICKPROOF     2
   CLOSED        4
   LOCKED        8

Value[2]: The item-number of the object which can open the object. -1 means
          no lockability.
Value[3]: Internal use for Corpses that must "rot".

ITEM_NOTE      (16)
Value[0]: Tounge (language of writing). Not yet used.
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_DRINKCON  (17)
Value[0]: Maximum drink-units the drink-container can contain.
Value[1]: Number of drink-units that are left in the container.
Value[2]: The type of liquid in the drink-container, one of:

              Type           nr.    Effect
                                    Drunkness   Fullness   Thirst

              LIQ_WATER      0      0           1           10
              LIQ_BEER       1      3           2           5
              LIQ_WINE       2      5           2           5
              LIQ_ALE        3      2           2           5
              LIQ_DARKALE    4      1           2           5
              LIQ_WHISKY     5      6           1           4
              LIQ_LEMONADE   6      0           1           8
              LIQ_FIREBRT    7      10          0           0
              LIQ_LOCALSPC   8      3           3           3
              LIQ_SLIME      9      0           4           -8
              LIQ_MILK       10     0           3           6
              LIQ_TEA        11     0           1           6
              LIQ_COFFE      12     0           1           6
              LIQ_BLOOD      13     0           2           -1
              LIQ_SALTWATER  14     0           1           -2
              LIQ_CLEARWATER 15	    0		0	    13

          The above values for drunkness/fullness/thirst are used per 
          four "units" drunk. The values are expressed in HOURS!
          Example:
            Dragon empties a bottle (say 7 units) of saltwater.
            His Drunkness is not changed ((7/4)*0)
            His Fullness increases by ((7/4)*1) hours
            His Thirst increases by ((7/4)*-2) hours, thus making
               him More thirsty.

          The hours above are numbers between 0 and 24. 24 hours is
          maximum for drunkness/fullness/thirst. When hours are zero
          for any drunkness/fullness/thirst the person will be
          sober, hungry, or thirsty respectively.

Value[3]: if this value is non-zero, then the drink is poisoned.

ITEM_KEY       (18)
Value[0]: The key-type. This value must match the lock-type the door
          that the key can open. 
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_FOOD      (19)
Value[0]: The number of hours, that this food will fill the stomach
Value[1]: -
Value[2]: -
Value[3]: If this value is non-zero, the food is poisoned.

ITEM_MONEY     (20)
Value[0]: The number of gold coins "in the pile of coins".
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_PEN       (21)
Value[0]: -
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_BOAT      (22)
Value[0]: -
Value[1]: -
Value[2]: -
Value[3]: -

ITEM_FOUNTAIN  (23)

Value[0]: Maximum drink-units the drink-container can contain.
Value[1]: Number of drink-units that are left in the container.
Value[2]: The type of liquid in the drink-container; definitions
	  are the same as for ITEM_DRINKCON.
Value[3]: Drink is posioned if non-zero.

-------------------------------------------------------------------------
                              IMPORTANT NOTICE!!

Since the level versus average damage calculations are performed as shown
in "spell_info.doc" all the offensive spells are individually much stronger
than higher level spells. For example:

  1 fireball of level 15 gives more damage than one of level 16 which give
    more damage than level 17 which give more damage than level 18 which
    give more.... etc.

  Thus please make all offensive potions/scrolls/etc. as the basic level
  they are designed for. You can see the level below. You can see the spells
  average damage, by looking at the spell versus the level of the spell,
  divided by the number of spells learned at that particular level. Example:

  Level 9 Lightning bolt give (102/2) average damage. Divided by 2, because
          this is the level at which it is first learned (You can almost
          always cast two spells are first learned level). At level 10 it
          gives (120/3) average damage.

Looking at the table in "spell_info.doc", you should know that each spell
is designed so that it will kill a victim three levels below the mana user,
if the manauser ONLY cast his spell....


-------------------------------------------------------------------------
The Y/N rows below indicate which potions/scrolls/wands/staffs that are
possible to make. The MIN_LEV is the Minimum Level DEMANDED by the item
you are making. The game will crash if making level less than demanded.

-------------------------------------------------------------------------
                                   POTION  SCROLL  WAND  STAFF   MIN_LEV
TYPE_UNDEFINED               -1      Y       Y      Y      Y        -
SPELL_RESERVED_DBC            0      N       N      N      N        -
SPELL_ARMOR                   1      Y       Y      Y      N        >0
SPELL_TELEPORT                2      Y       Y      Y      Y        >0
SPELL_BLESS                   3      Y       Y      Y      N        ...
SPELL_BLINDNESS               4      Y       Y      Y      Y
SPELL_BURNING_HANDS           5      N       N      N      N        == 5
SPELL_CALL_LIGHTNING          6      Y       Y      N      Y        >=12
SPELL_CHARM_PERSON            7      N       Y      N      Y
SPELL_CHILL_TOUCH             8      N       N      N      N        == 3
SPELL_CLONE                   9      Y       Y      Y      N
SPELL_COLOUR_SPRAY           10      N       Y      Y      N        ==11

SPELL_CONTROL_WEATHER        11      N       N      N      N
SPELL_CREATE_FOOD            12      N       Y      N      N
SPELL_CREATE_WATER           13      N       N      N      N
SPELL_CURE_BLIND             14      Y       N      N      Y
SPELL_CURE_CRITIC            15      Y       N      N      Y
SPELL_CURE_LIGHT             16      Y       N      N      Y
SPELL_CURSE                  17      Y       Y      N      Y
SPELL_DETECT_EVIL            18      Y       N      N      Y
SPELL_DETECT_INVISIBLE       19      Y       N      N      Y
SPELL_DETECT_MAGIC           20      Y       N      N      Y

SPELL_DETECT_POISON          21      Y       Y      N      N
SPELL_DISPEL_EVIL            22      Y       Y      Y      Y       == 10
SPELL_EARTHQUAKE             23      N       Y      N      Y       == 7
SPELL_ENCHANT_WEAPON         24      N       Y      N      N
SPELL_ENERGY_DRAIN           25      Y       Y      Y      Y       == 13
SPELL_FIREBALL               26      N       Y      Y      N       == 15
SPELL_HARM                   27      Y       N      N      Y       == 15
SPELL_HEAL                   28      Y       N      N      Y
SPELL_INVISIBLE              29      Y       Y      Y      Y
SPELL_LIGHTNING_BOLT         30      N       Y      Y      N       == 9

SPELL_LOCATE_OBJECT          31      N       N      N      N
SPELL_MAGIC_MISSILE          32      N       Y      Y      N       == 1
SPELL_POISON                 33      Y       N      N      Y
SPELL_PROTECT_FROM_EVIL      34      Y       Y      Y      Y
SPELL_REMOVE_CURSE           35      Y       Y      N      Y
SPELL_SANCTUARY              36      Y       Y      N      Y
SPELL_SHOCKING_GRASP         37      N       N      N      N       == 7
SPELL_SLEEP                  38      Y       Y      Y      Y
SPELL_STRENGTH               39      Y       Y      N      Y
SPELL_SUMMON                 40      N       N      N      N

SPELL_VENTRILOQUATE          41      N       N      N      N
SPELL_WORD_OF_RECALL         42      Y       Y      Y      Y
SPELL_REMOVE_POISON          43      Y       N      N      Y
SPELL_SENCE_LIFE             44      Y       N      N      Y

SPELL_IDENTIFY              *53*     N       Y      N      N
