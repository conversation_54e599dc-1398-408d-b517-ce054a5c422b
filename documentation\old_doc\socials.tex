\documentclass[11pt]{article}
\usepackage{url}
\usepackage{times}
\usepackage[T1]{fontenc}
% Document typeset from the original document that was typeset by <PERSON>.
% This document typeset by <PERSON> <<EMAIL>> on Dec 4/2001

\addtolength{\topmargin}{-.5in}       % repairing LaTeX's huge  margins...
\addtolength{\textheight}{1in}        % more margin hacking
\addtolength{\textwidth}{1in}         % and here...
\addtolength{\oddsidemargin}{-0.5in}
\addtolength{\evensidemargin}{-0.5in}
\setlength{\parskip}{\baselineskip}
\setlength{\parindent}{20pt}

\title{CircleMUD Socials}
\author{<PERSON>}
\begin{document}

\maketitle

\begin{abstract}
This document is a description of how to create new `social' commands in CircleMUD. C programming experience generally not required at all for creating new CircleMUD socials.  The intended audience for this document is all CircleMUD administrators.  This document ties together closely with the document on the \texttt{act()} function.
\end{abstract}

\section{Introduction}
A general system exists to handle the `social' commands -- those which generally have no game-related purpose other than to convey emotions between players.   Socials are also useful for creating advanced DikuMud adventures and quests through the use of speical procedures; for example, you could add a `detonate' social which, within your quest, is handled by a special procedure to cause something to explode.
\par
Socials are all handled through the generalized command \texttt{do\_action}.  The
text file \texttt{lib/text/socials} contains information on the game's socials. New socials can be added by 1) adding the name and level of the social to the master command list in \texttt{interpreter.c} (giving \texttt{do\_action} as the function pointer), and 2) adding information for the new social to the \texttt{lib/text/socials} file.

\section{File Format}
In Circle 3.1, socials in the file are specified by name (instead of by command number, as was true of the original DikuMud and versions of CircleMUD before 3.0.).  In the standard CircleMUD distribution, the socials appear in alphabetical order in the file, but they can appear in any order and can be rearranged if necessary.
\par
The file is formatted as follows:
\begin{verbatim}
<command name> <hide-flag> <minimum position of victim>
<messg to character if no argument>
<messg to others if no argument>
<messg to char if victim found>           <---If this field is empty,
<messg to others if victim found>          <- 
<messg to victim>                            | then these fields must be
<messg to char if victim not found>          | skipped, and the action will
<messg to char if vict is char>              | ignore arguments.
<messg to others if vict is char>          <-

<command name> <hide-flag> <minimum position of victim>
.
.
$~
\end{verbatim}
Each social must contain either 1) only the first two messages (if the social ignores arguments), or 2) all eight messages (if the social takes an argument). Each message must be contained in one line.
\par
The command-name indicates which social is being specified.  The hide-flag can be either 0 or 1; if 1, the social is hidden from OTHERS if they cannot see the character performing the social.  The action is not hidden from the VICTIM, even if s/he cannot see the character performing the social, although in such cases the character's name will, of course, be replaced with ``\texttt{someone}''.
\par
Where it makes sense to do so, text fields may be left empty. This is done by putting a `\#' in the first column on the line.  Doing so is legal in the following fields:
\begin{itemize}
\item a: messg to others if no arg
\item b: messg to others if victim found
\item c: messg to others if vict is char
\end{itemize}

Note again that if the field {\it messg to char if victim found} is empty, then the following fields must be omitted entirely (not even the `\~{}'), and the action will ignore any arguments supplied.
\par
The procedure sends the text strings through \texttt{act()}, and they may contain control codes (see the documentation for the \texttt{act()} function for details.) Note that not all of \texttt{act()}'s control codes may be used; for example, codes which refer to objects such as \texttt{\$p}.
\par
For the sake of efficiency, no tests or sanity checks are made on the consistency or logic of the \texttt{act()} codes found in the social messages. Hence, grave disorder may result if, say, the code `\texttt{\$N}' occurs in a text field that does not refer to a victim; like {\it messg to others if no arg}.
In previous versions of Circle, such grave disorder often manifested itself in the form of crashes.  However, the \texttt{act()} function itself in CircleMUD 3.0 and above does sanity checks to make sure it is not dereferencing null pointers, hopefully with the result of avoiding crashes.  Nevertheless, great care should be taken in making sure that the \$ codes of socials are used consistently and correctly.
\par
More information can be found in the documentation on the \texttt{act()} function.

\end{document}
\end
