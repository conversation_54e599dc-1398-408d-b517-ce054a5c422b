==========
act.movement.c
----------
player_specials


==========
act.movement.c
----------
GET_SKILL


==========
act.offensive.c
----------
GET_SKILL


==========
act.other.c
----------
do_practice
list_skills
GET_SKILL


==========
act.wizard.c
----------
GET_PRACTICES
MAX_SKILLS
SET_SKILL
player_specials


==========
comm.c
----------
player_specials


==========
class.c
----------
GET_PRACTICES
LEARNED
SET_SKILL


==========
db.c
----------
MAX_SKILLS
SET_SKILL
sort_spells
player_specials


==========
dg_variables.c
----------
GET_PRACTICES
GET_SKILL
find_skill_num
SET_SKILL


==========
dg_ misc.c
----------
spell_info
find_skill_num


==========
graph.c
----------
GET_SKILL


==========
interpreter.c
----------
do_practice
player_specials


==========
magic.c
----------
spell_info


==========
medit.c
----------
player_specials


==========
modify.c
----------
spell_info
find_skill_num
LEARNED
SET_SKILL


==========
oedit.c
----------
spell_info


==========
players.c
----------
GET_PRACTICES
MAX_SKILLS
GET_SKILL
player_specials
PFDEF_PRACTICES


==========
quest.c
----------
player_specials


==========
spell_parser.c
----------
spell_info
GET_SKILL


==========
spec_procs.c
----------
list_skills
GET_PRACTICES
SPLSKL
MAX_SKILLS
spell_sort_info
spell_info
how_good
GET_SKILL
SPECIAL(guild)
find_skill_num
LEARNED
SET_SKILL
sort_spells




**********
==========
utils/plrtoascii.c
----------
MAX_SKILLS
player_specials
spells_to_learn
PFDEF_PRACTICES
**********



**********
==========
act.h
----------
do_practice


==========
pfdefaults.h
----------
PFDEF_PRACTICES


==========
spec_procs.h
----------
list_skills
SPECIAL(guild)
sort_spells


==========
spells.h
----------
MAX_SKILLS
spell_info
find_skill_num


==========
structs.h
----------
MAX_SKILLS
player_specials
spells_to_learn


==========
utils.h
----------
GET_PRACTICES
spell_info
GET_SKILL
SET_SKILL
player_specials
spells_to_learn



**********
==========
----------