\documentclass[11pt]{article}
\usepackage{url}
\usepackage{times}
\usepackage[T1]{fontenc}
% Document typeset from the original document that was typeset by <PERSON>.
% This document typeset by <PERSON> <<EMAIL>> on Dec 9/2001

\addtolength{\topmargin}{-.5in}       % repairing LaTeX's huge  margins...
\addtolength{\textheight}{1in}        % more margin hacking
\addtolength{\textwidth}{1in}         % and here...
\addtolength{\oddsidemargin}{-0.5in}
\addtolength{\evensidemargin}{-0.5in}
\setlength{\parskip}{\baselineskip}
\setlength{\parindent}{0pt}

\title{CircleMUD Release History}
\author{<PERSON>}
\begin{document}

\maketitle

\begin{abstract}
This document is basically just a a compliation of all the \texttt{README} files which accompanied each release of CircleMUD.  At the end is the post to \texttt{rec.games.mud.diku} which originally anounced CircleMUD as a publically available MUD source code.
\end{abstract}

\begin{itemize}
\item Version 2.20:  November 17, 1993
\item Version 2.11:  September 19, 1993
\item Version 2.10:  September 1, 1993
\item Version 2.02:  Early August
\item Version 2.01:  July 20, 1993
\item Version 2.00:  July 16, 1993
\end{itemize}

The latest production version of Circle is 2.20, released on November 17, 1993.  Version 2.20 supercedes version 2.11, which was released on September 19, 1993.

\section{Version 2.20 - November 17, 1993}
New features:
\begin{itemize}
\item A completely new output buffering system which is far more network-efficient, and somewhat more memory- and speed-efficient, than the original Diku system. Definitely a major win for people with slow Net links.  (Details available by request, but this was discussed on rgmd recently.)  Several other functions (such as do\_where() and do\_who()) have been rewritten to take advantage of the new system.
\item Redesigned stat screens with better readability
\item Command-line substitution via the ``\^{}'' character (works identically to the csh command)
\item Code sent by Jeff Fink (thanks Jeff!): Help now handles ambiguous cases correctly (i.e., ``help color'' will give you help for color and not colorspray)
\item vstat command to stat mobiles and object by virtual number
\item updated documentation
\end{itemize}
And, bug fixes of varying degrees of severity:
\begin{itemize}
\item SunOS Bus errors on stealing
\item +hit item bug
\item Switched immort re-login bug
\item Mob memory bug
\item Poison/Stat bug (I think this one is native to Diku Gamma 0.0 -- the function hit\_gain was responsible for subtracting hits when a char is poisoned, so you'd lose hits when someone statted you.)
\item Stat room bug under Solaris and IRIX
\item Ungroup bug
\item ``goto 3.guard'' now works (takes you to the third guard instead of room 3)
\item various other minor fixes
\end{itemize}

\section{Version 2.11 - September 19, 1993}
Changes in 2.11 (from 2.10):\newline
Mostly bug fixes, including:
\begin{itemize}
\item SET FILE bug
\item SIGBUS/unaligned data errors under SunOS and other OS's
\item Move limit modifier bug
\item wrist-wearing bug
\item Compilation problems with utility.c under some operating systems
\end{itemize}
The only notable change is that the hit\_limit, move\_limit, and mana\_limit functions have been removed (for many reasons).  From the players' point of view, this means that a character no longer gains movement points with age. Hit, move, and mana gain speeds are still a function of age, however.

\section{Version 2.10 - September 1, 1993}
Changes in 2.10 (from 2.01):
\begin{itemize}
\item Rewritten get/put/drop/junk/donate/give/wear/remove, so that ``all'' and ``all.x'' work in a much wider variety of cases.  Loosely based on code sent in by Jeff Fink.
\item ``Track'' function based on breadth-first search
\item Configurable auto-save feature to automatically crash-save players periodically
\item More intense error-checking in object saving system to detect problems with file permissions
\item Many configuration options added to config.c
\item  Option to make death traps automatically have dump spec-proc assigned
\item ASPELL and ACAST macros added to match the ACMD macros; spells1.c, spells2.c, spell\_parser.c, and magic.c changed to use the macros.
\item SKILL macro split into GET\_SKILL and SET\_SKILL macros so that error checking can be done
\item Enhanced documentation -- a help entry now exists for every command
\item Linux compatibility, and further steps to SVR4 compatibility which will make it into Circle eventually.  (Note: you must make a change in one line of the Makefile for Linux compatibility.)
\item All functions now prototyped before use
\end{itemize}

Jeremy Elson\newline
August 31, 1993

\section{Version 2.01 - July 20, 1993}
Version 2.01 is basically the same as 2.00; most of the changes are for making the MUD more portable, based on mail I've received after the release of version 2.00.
\begin{itemize}
\item Problems with OPEN\_MAX and SEEK\_x resolved
\item Some problems with the Makefile fixed
\item Compiles much more cleanly with the -Wall option
\item A couple of minor bugs fixed
\item A few small fixes to the documentation
\end{itemize}
July 20, 1993

\section{Version 2.00 - July 16, 1993}
CircleMUD was developed and tested under Ultrix 4.0; your mileage may vary. If I have time, I'll try and port it to other machines.  If you port it and want to share your work with others, feel free to drop me a line.
\par
The CircleMUD `press release' is included below, in case you haven't seen it and want to.
\par
Good Luck!
\par
Jeremy Elson aka Rasmussen (Ras)\newline
July 16, 1993

\begin{verbatim}
Wake the kids and find the dog, because it's the FTP release of


                    C  I  R  C  L  E  M  U  D     2  .  0


That's right -- CircleMUD 2.0 is done and is now available for anonymous FTP
at ftp.cs.jhu.edu!

CircleMUD is highly developed from the programming side, but highly UNdeveloped
on the game-playing side.  So, if you're looking for a huge MUD with billions
of spells, skills, classes, races, and areas, Circle will probably disappoint
you severely.  Circle still has only the 4 original Diku classes, the original
spells, the original skills, and about a dozen areas.

On the other hand, if you're looking for a highly stable, well-developed,
well-organized "blank slate" MUD on which you can put your OWN ideas for
spells, skills, classes, and areas, then Circle might be just what you're
looking for.

Just take a gander at some of Circle's nifty features:

        -- In-memory mobile and object prototypes and string sharing for
           decreased memory usage and blazingly fast zone resets

        -- All large realloc()s have been removed and replaced by boot-time
           record counting and a single malloc() for superior memory efficiency

        -- Split world/obj/mob/zon/shp files for easy addition of areas; plus,
           all the world files are still in the original Diku format for
           compatibility with existing areas

        -- Boot-time and run-time error checking of most data files with
           diagnostic messages a lot more helpful than "segmentation fault"!

        -- Player mail system and bank

        -- Rewritten board system: boards are now stable, robust, more
           intelligent, and easily expandable -- adding a new board is
           as easy as adding another line to an array

        -- ANSI color codes with a fully documented programmers' interface

        -- On-line system logs

        -- Optional automatically regenerating wizlist -- a final end
           to new immortals constantly asking you when they'll be added
           to the immlist!

        -- "config.c" file allows you to change aspects of the game such
           as playerkilling/playerthieving legality, max number of objects
           rentable, and nameserver usage -- WITHOUT recompiling the
           entire MUD!

        -- All text (help, mortal/immort MOTDs, etc.) is rebootable at
           run-time with the "reboot" command

        -- All players are given a unique serial number -- no more messy,
           time consuming str_cmp()s when you're trying to identify people!

        -- Fully integrated and robust rent/crash system -- allows normal
           renting, cryo-renting, crash protection, and forced rent
           (at an increased price) after an hour of idling

        -- All the standard wizard stuff you're used to: level-sensitive
           invisibility, settable poofin/poofouts, wizline

        -- Advanced 'set' command which allows you to set dozens of aspects
           of players -- even if they aren't logged in!  "Stat" also allows
           you to stat people who aren't logged in!

        -- Intelligent 'autorun' script handles different types of reboots,
           organizing your system logs, and more!

        -- Circle comes with more than a dozen utilities, all fully
           documented, to make maintenance a snap!

        -- And much, much more!

Unfortunately, the original Circle had more than its fair share of Bad People
when it was alive, but it DID lead to an impressive list of security and
"asshole control" features:

        -- 3 types of sitebanning available: 'all' to refuse all connections,
           'new' to refuse new players, or 'select' to refuse new players and
           all registered players who don't have a SITEOK flag.

        -- 'wizlock' allows you to close the game to all new players or all
           players below a certain level.

        -- Handy 'mute' command squelches a player off of all public
           communication channels

        -- Handy 'freeze' command freezes a player in his tracks: the MUD
           totally ignores all commands from that player until he's thawed.

        -- Even handier DELETE flag allows you to delete players on the fly.

        -- 'set' command (mentioned above) allows you to freeze/unfreeze/
           delete/siteok/un-siteok players -- even if they aren't logged in!

        -- Bad password attempts are written to the system log and saved;
           if someone tries to hack your account, you see "4 LOGIN FAILURES
           SINCE LAST SUCCESSFUL LOGIN" next time you log on.

        -- Passwords don't echo to the screen; allows 3 bad PW attempts
           before disconnecting you.  

        -- Players aren't allowed to choose their character's name as their
           password -- you'd be surprised how many do!

        -- "xnames" text file specifies a list of invalid name substrings
           to prevent creation of characters with overly profane names.

Listen to all the rave reviews of CircleMUD 2.0!

        "How long ago was that deadline you set for yourself?" -- My Friend

        "NO ONE should be denied the power of computation." -- My Professor

        "Multi-user WHAT?" -- My Mom

Give it a try -- what do you have to lose other than your GPA/job, friends,
and life?

---------------------------------------------------------------------------

Circle's complete source code and areas are available now for anonymous FTP
at ftp.cs.jhu.edu (128.220.13.50) in the directory pub/CircleMUD.

I welcome comments and constructive criticism about CircleMUD and would be
happy to discuss any design decisions I've made, but I'm not particularly
receptive to lunatics frothing at the mouth and thus will probably ignore
you if you flame me.

Also, remember the odds here: one person (me) against 29,000 lines of
code (Circle), so there are bound to be some mistakes in there somewhere.

Good luck, and happy Mudding,

  Jeremy Elson aka Ras

\end{verbatim}

\end{document}
\end
