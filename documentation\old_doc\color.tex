\documentclass[11pt]{article}
\usepackage{url}
\usepackage{times}
\usepackage[T1]{fontenc}
% Document typeset from the original document that was typeset by <PERSON>.
% This document typeset by <PERSON> <<EMAIL>> on Dec 4/2001

\addtolength{\topmargin}{-.5in}       % repairing LaTeX's huge  margins...
\addtolength{\textheight}{1in}        % more margin hacking
\addtolength{\textwidth}{1in}         % and here...
\addtolength{\oddsidemargin}{-0.5in}
\addtolength{\evensidemargin}{-0.5in}
\setlength{\parskip}{\baselineskip}
\setlength{\parindent}{20pt}
\newenvironment{Ventry}[1]
  {\begin{list}{}{\renewcommand{\makelabel}[1]{\textsf{##1:}\hfil}
    \settowidth{\labelwidth}{\textsf{#1:}}
    \setlength{\leftmargin}{\labelwidth}
    \addtolength{\leftmargin}{\labelsep}
  }
  }
  {\end{list}}

\title{Using Color In CircleMUD}
\author{<PERSON>}
\begin{document}

\maketitle

\begin{abstract}
This document is a description of how to write C code which displays messages to players in color in the CircleMUD game engine.  Its intended audience is for Coders of CircleMUD.
\end{abstract}

CircleMUD allows you to create colorful messages by using ANSI control sequences.  Each player may select what ``level'' of color he/she desires from the four levels ``off,'' ``sparse,'' ``normal,'' and ``complete.''  Each player can select his/her color level by using the \texttt{COLOR} command from within the MUD; you as the programmer must decide which messages will be colored for each of the color levels.
\par
All files in which you wish to use color must have the line:
\begin{verbatim}
  #include "screen.h"
\end{verbatim}
This should be put in after all other includes in the beginning of the file.
\par
There are 8 colors available -- ``normal,'' red, green, yellow, blue, magenta, cyan and white.  They are accessible by sending control sequences as part of another string, for example:
\begin{verbatim}
  sprintf(buf, "If you're %shappy%s and you know it clap "
               "%d of your hands.\n\r", x, y, num_of_hands);
  send_to_char(buf, ch);
\end{verbatim}
In this example, \texttt{x} and \texttt{y} are the ``on'' and ``off'' sequences for the color you want.  There are 2 main series of color macros available for you to use (don't actually use ``x'' and ``y,'' of course!): the K series and the CC series. The CC (Conditional Color) series is recommended for most general use.
\par
The name of the actual sequence starts with the name of its series, plus a 3-letter color code, as follows:
\begin{description}
\item[Normal:] NRM
\item[Red:] RED
\item[Yellow:] YEL
\item[Green:] GRN
\item[Blue:] BLU
\item[Magenta:] MAG
\item[Cyan:] CYN
\item[White:] WHT
\end{description}
For example, white in the K series is \texttt{KWHT}; blue in the CC series is \texttt{CCBLU()} (arguments defined below).
\par
The K series requires no arguments, and is simply a macro to the ANSI color code.  Therefore, if you use a K-series color code, the color will ALWAYS be sent, even if the person you're sending it to has color off. This can very bad -- some people who do not have ANSI-compatible terminals will see garbage characters instead of colors.  If the terminal correctly ignores ANSI color codes, then nothing will show up on their screen at all.  The K series is mainly used to print colors to a string if the player's color level will later be tested manually (for an example, see \texttt{do\_gen\_com} in \texttt{act.comm.c}).
\par
The recommended series is the CC series (i.e.\ \texttt{CCNRM()}, \texttt{CCRED()}, etc.) The CC series macros require two arguments -- a pointer to the character to whom the string is being sent, and the minimum color level the player must be set to in order to see the color.  Color sent as `sparse' (\texttt{C\_SPR}) will be seen by people with color set to sparse, normal, or complete;
color sent as `normal' (\texttt{C\_NRM}) will be seen only by people with color set to normal or complete; color sent as `complete' (\texttt{C\_CMP}) will be seen only by people with color set to complete.
\par
To illustrate the above, an example is in order:
\begin{verbatim}
#include "screen.h"
/* include screen.h in all files that you use color in */

ACMD(do_showcolor)
{
  char buf[300];

  sprintf(buf, "Don't you just love %scolor%s, %scolor%s, "
               "%sCOLOR%s!\n\r",
      CCBLU(ch, C_CMP), CCNRM(ch, C_CMP),
      CCYEL(ch, C_NRM), CCNRM(ch, C_NRM),
      CCRED(ch, C_SPR), CCNRM(ch, C_SPR));
  send_to_char(buf, ch);
}
\end{verbatim}
What does this do?  For people with color set to Complete, it prints:
\begin{verbatim}
    Don't you just love color, color, COLOR!
                       (blue)  (yellow)  (red)
\end{verbatim}
People who have color set to Normal will see:
\begin{verbatim}
    Don't you just love color, color, COLOR!
                               (yellow)  (red)
\end{verbatim}
People who have color set to Sparse will see:
\begin{verbatim}
    Don't you just love color, color, COLOR!
                                         (red)
\end{verbatim}
People who have color set to Off will see:
\begin{verbatim}
    Don't you just love color, color, COLOR!
                    (no color, as you'd expect)
\end{verbatim}
There are several common pitfalls with using the CC series of color macros:
\begin{itemize}
\item Do not confuse \texttt{CCNRM} with \texttt{C\_NRM}.  \texttt{CCNRM()} is a macro to turn the color back to normal; \texttt{C\_NRM} is a color level of ``normal''.
\item Always make sure that every pair of ``on'' and ``off'' codes are at the same color level.  For example:
\begin{verbatim}
WRONG:  sprintf(buf, "%sCOLOR%s\n\r", CCBLU(ch, C_NRM),
                                      CCNRM(ch, C_CMP));
\end{verbatim}
\par
This is wrong because if someone has their color level set to Normal, the \texttt{CCBLU} code will be sent but the \texttt{CCNRM} command will not, causing all subsequent output to be blue.
\begin{verbatim}
WRONG:  sprintf(buf, "%sCOLOR%s\n\r", CCBLU(ch, C_CMP),
                                      CCNRM(ch, C_NRM));
\end{verbatim}
The above statement is also wrong, although not as bad.  In this case, someone with color set to Normal will (correctly) not get the \texttt{CCBLU} code, but will then unnecessarily get the \texttt{CCNRM} code.  Never send a color code if you don't have to -- the codes are several bytes long, and cause a noticeable pause at 2400 baud.
\par
\item This should go without saying, but don't ever send color at the \texttt{C\_OFF} level.
\item Special precautions must be taken when sending a colored string to a large group of people -- you can't use the color level of ``ch'' (the person sending the string) -- each person receiving the string must get a string appropriately colored for his/her level.  In such cases, it is usually best to set up two strings (one colored and one not), and test each player's color level individually (see \texttt{do\_gen\_com} in \texttt{act.comm.c} for an example).
\end{itemize}

\end{document}
\end
